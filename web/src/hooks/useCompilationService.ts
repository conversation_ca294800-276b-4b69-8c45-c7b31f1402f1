/**
 * 编译服务 Hook
 * 提供高级编译服务功能，封装了 WebSocket 通信和智能编译策略
 * 为UI组件提供简洁的编译接口，与底层WebSocket实现解耦
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { useRealtimeCompilation } from './useRealtimeCompilation'
import type { CompilationResult, CompilationProgress } from './useRealtimeCompilation'

interface CompilationServiceOptions {
  backendUrl?: string
  autoPrecompile?: boolean
  precompileDelay?: number
  enableSmartCompilation?: boolean
}

export interface CompilationServiceReturn {
  // 编译状态
  isCompiling: boolean
  isConnected: boolean
  lastCompiledContent: string
  compilationResult: CompilationResult | null
  progress: CompilationProgress | null
  error: string | null
  
  // 编译控制
  compile: (content: string, force?: boolean) => Promise<void>
  precompile: (content: string) => void
  canCompile: (content: string) => boolean
  needsCompilation: (content: string) => boolean
  
  // 连接管理
  ensureConnected: () => void
  disconnect: () => void
  
  // 结果管理
  clearError: () => void
  hasResult: () => boolean
}

export function useCompilationService(
  options: CompilationServiceOptions = {}
): CompilationServiceReturn {
  const {
    backendUrl,
    autoPrecompile = true,
    precompileDelay = 1000,
    enableSmartCompilation = true,
  } = options

  // 高级状态管理
  const [lastCompiledContent, setLastCompiledContent] = useState<string>('')
  const [isPrecompiling, setIsPrecompiling] = useState(false)
  const precompileTimeoutRef = useRef<NodeJS.Timeout>()
  const compiledMarkdownRef = useRef<string>('')

  // 使用底层实时编译hook
  const {
    isConnected,
    isCompiling,
    result,
    progress,
    error,
    compile: wsCompile,
    canCompile: wsCanCompile,
    connect,
    disconnect: wsDisconnect,
  } = useRealtimeCompilation({
    debounceMs: 300,
    autoCompile: false, // 手动控制编译时机
    backendWsUrl: backendUrl,
  })

  // 当编译结果返回时，更新最后编译的内容
  useEffect(() => {
    if (result && compiledMarkdownRef.current) {
      setLastCompiledContent(compiledMarkdownRef.current)
      compiledMarkdownRef.current = ''
    }
  }, [result])

  // 核心编译方法
  const compile = useCallback(async (content: string, force = false) => {
    if (!content.trim()) {
      return
    }

    // 取消任何正在进行的预编译
    if (precompileTimeoutRef.current) {
      clearTimeout(precompileTimeoutRef.current)
      setIsPrecompiling(false)
    }

    // 确保连接
    if (!isConnected) {
      connect()
    }

    if (!wsCanCompile(content)) {
      console.warn('[CompilationService] 无法编译：WebSocket未连接或内容为空')
      return
    }

    // 智能编译：避免重复编译相同内容
    if (enableSmartCompilation && !force && content === lastCompiledContent && result) {
      console.log('[CompilationService] 跳过编译：内容未变更')
      return
    }

    console.log('[CompilationService] 开始编译')
    compiledMarkdownRef.current = content
    wsCompile(content, force)
  }, [isConnected, wsCanCompile, connect, wsCompile, lastCompiledContent, result, enableSmartCompilation])

  // 智能预编译
  const precompile = useCallback((content: string) => {
    if (!autoPrecompile || !content.trim() || content === lastCompiledContent) {
      return
    }

    // 如果正在编译，跳过预编译
    if (isCompiling) {
      return
    }

    // 清除之前的预编译定时器
    if (precompileTimeoutRef.current) {
      clearTimeout(precompileTimeoutRef.current)
    }

    setIsPrecompiling(true)
    precompileTimeoutRef.current = setTimeout(async () => {
      try {
        // 再次检查状态，确保没有其他编译正在进行
        if (!isCompiling && content !== lastCompiledContent) {
          console.log('[CompilationService] 触发智能预编译')
          await compile(content, false)
        } else {
          console.log('[CompilationService] 跳过预编译：正在编译或内容未变更')
        }
      } catch (error) {
        console.warn('[CompilationService] 预编译失败:', error)
      } finally {
        setIsPrecompiling(false)
      }
    }, precompileDelay)
  }, [autoPrecompile, precompileDelay, lastCompiledContent, isCompiling, compile])

  // 检查是否需要编译
  const needsCompilation = useCallback((content: string) => {
    return !!content.trim() && content !== lastCompiledContent && (!result || lastCompiledContent !== content)
  }, [lastCompiledContent, result])

  // 确保连接
  const ensureConnected = useCallback(() => {
    if (!isConnected) {
      connect()
    }
  }, [isConnected, connect])

  // 清除错误
  const clearError = useCallback(() => {
    // 这里可以添加清除错误的逻辑，但当前hook没有提供这个功能
    console.log('[CompilationService] 清除错误状态')
  }, [])

  // 检查是否有结果
  const hasResult = useCallback(() => {
    return !!result
  }, [result])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (precompileTimeoutRef.current) {
        clearTimeout(precompileTimeoutRef.current)
      }
    }
  }, [])

  return {
    // 编译状态
    isCompiling: isCompiling || isPrecompiling,
    isConnected,
    lastCompiledContent,
    compilationResult: result,
    progress,
    error,
    
    // 编译控制
    compile,
    precompile,
    canCompile: wsCanCompile,
    needsCompilation,
    
    // 连接管理
    ensureConnected,
    disconnect: wsDisconnect,
    
    // 结果管理
    clearError,
    hasResult,
  }
}