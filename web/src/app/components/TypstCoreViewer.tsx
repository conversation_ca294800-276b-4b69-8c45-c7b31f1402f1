"use client";
import { useEffect, useRef, useState } from "react";

interface TypstRenderer {
  render: (options: {
    artifactContent: Uint8Array;
    format: "vector";
    backgroundColor: string;
    container: HTMLElement;
    pixelPerPt: number;
  }) => Promise<void>;
}

// Core renderer API (aligns with typst-playground)
// We import from ESM paths to avoid wrapper specifics.
// Types are declared in web/src/types/typst-core.d.ts
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { withGlobalRenderer } from "@myriaddreamin/typst.ts/dist/esm/contrib/global-renderer.mjs";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { createTypstRenderer } from "@myriaddreamin/typst.ts/dist/esm/renderer.mjs";

type Props = {
  artifactBlob: Blob | Uint8Array;
  className?: string;
  fill?: string; // background color
};

export default function TypstCoreViewer({ artifactBlob, className, fill = "#ffffff" }: Props) {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let alive = true;
    (async () => {
      try {
        setLoading(true);
        setError(null);
        const artifact = artifactBlob instanceof Uint8Array
          ? artifactBlob
          : new Uint8Array(await artifactBlob.arrayBuffer());

        await withGlobalRenderer(createTypstRenderer, {
          getModule: () => "/typst/typst_ts_renderer_bg.wasm",
        }, (renderer: TypstRenderer) => {
          if (!alive) return;
          const el = containerRef.current;
          if (!el) return;
          return renderer.render({
            artifactContent: artifact,
            format: "vector",
            backgroundColor: fill,
            container: el,
            pixelPerPt: 3,
          });
        });
      } catch (e: unknown) {
        setError(String(e instanceof Error ? e.message : e));
      } finally {
        if (alive) setLoading(false);
      }
    })();
    return () => { alive = false; };
  }, [artifactBlob, fill]);

  if (error) return <div role="alert" style={{ color: "#c00" }}>Load failed: {error}</div>;
  return (
    <div className={className}>
      {loading && <div aria-busy>Loading typst…</div>}
      <div ref={containerRef} style={{ width: "100%" }} />
    </div>
  );
}
